{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^6.1.10", "@mui/material": "^6.1.10", "@mui/x-date-pickers": "^7.22.3", "@tanstack/react-query": "^5.64.1", "@types/js-cookie": "^3.0.6", "@types/react-router-dom": "^5.3.3", "axios": "^1.7.9", "file-saver": "^2.0.5", "framer-motion": "^11.18.0", "joi": "^17.13.3", "js-cookie": "^3.0.5", "lucide-react": "^0.459.0", "moment": "^2.30.1", "process": "^0.11.10", "react": "^18.3.1", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.55.0", "react-query": "^3.39.3", "react-router-dom": "^6.28.0", "react-table": "^7.8.0", "react-toastify": "^11.0.0", "xlsx": "^0.18.5", "zustand": "^5.0.1"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/file-saver": "^2.0.7", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-table": "^7.7.20", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10", "vite-plugin-static-copy": "^2.2.0"}}